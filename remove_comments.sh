#!/bin/bash

# Script to remove comments from Kotlin project files
# This script removes:
# - Single-line comments (//)
# - Multi-line comments (/* */)
# - KDoc comments (/** */)

echo "Starting comment removal process..."

# Function to remove comments from Kotlin files
remove_kotlin_comments() {
    local file="$1"
    echo "Processing Kotlin file: $file"
    
    # Create a temporary file
    temp_file=$(mktemp)
    
    # Use sed to remove comments while preserving strings
    # This is a simplified approach - for production use, a proper parser would be better
    sed -E '
        # Remove single-line comments (but not in strings)
        s|//.*$||g
        # Remove multi-line comment start and end on same line
        s|/\*.*\*/||g
    ' "$file" > "$temp_file"
    
    # Handle multi-line comments that span multiple lines
    awk '
        BEGIN { in_comment = 0 }
        /\/\*/ && !/\*\// { 
            in_comment = 1
            gsub(/\/\*.*/, "")
            if (length($0) > 0) print $0
            next
        }
        /\*\// && in_comment { 
            in_comment = 0
            gsub(/.*\*\//, "")
            if (length($0) > 0) print $0
            next
        }
        !in_comment { print }
    ' "$temp_file" > "${temp_file}.2"
    
    # Remove empty lines that were left after comment removal
    sed '/^[[:space:]]*$/d' "${temp_file}.2" > "$file"
    
    # Clean up
    rm "$temp_file" "${temp_file}.2"
}

# Function to remove comments from Gradle files
remove_gradle_comments() {
    local file="$1"
    echo "Processing Gradle file: $file"
    
    # Remove single-line comments from Gradle files
    sed -i.bak -E 's|//.*$||g' "$file"
    
    # Remove empty lines
    sed -i.bak '/^[[:space:]]*$/d' "$file"
    
    # Remove backup file
    rm "${file}.bak"
}

# Process all Kotlin files
find . -name "*.kt" -type f | while read -r file; do
    remove_kotlin_comments "$file"
done

# Process Gradle files
find . -name "*.gradle.kts" -type f | while read -r file; do
    remove_gradle_comments "$file"
done

find . -name "*.gradle" -type f | while read -r file; do
    remove_gradle_comments "$file"
done

echo "Comment removal process completed!"
echo "Note: Please test your build to ensure everything still works correctly."
