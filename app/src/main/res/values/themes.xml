<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Theme.CareerWorx" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent</item>
        <item name="colorSecondaryVariant">@color/accent_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnBackground">@color/on_background</item>
        <item name="colorOnSurface">@color/on_surface</item>
        <!-- Error colors -->
        <item name="colorError">@color/error</item>
        <item name="colorOnError">@color/white</item>
        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <!-- Card colors -->
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="cardForegroundColor">@color/on_surface</item>
        <!-- Outline color -->
        <item name="colorOutline">@color/colorOutline</item>
        <!-- Overflow menu icon color -->
        <item name="actionOverflowButtonStyle">@style/OverflowButtonStyle</item>
    </style>

    <!-- Splash Screen Theme -->
    <style name="Theme.CareerWorx.Splash" parent="Theme.CareerWorx">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:statusBarColor">@color/primary</item>
        <item name="android:navigationBarColor">@color/primary</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>

    <!-- Button Styles -->
    <style name="Widget.CareerWorx.Button" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/primary</item>
        <item name="android:textColor">@color/white</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
    </style>

    <style name="Widget.CareerWorx.Button.Secondary" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/button_secondary</item>
        <item name="android:textColor">@color/white</item>
        <item name="cornerRadius">8dp</item>
    </style>

    <style name="Widget.CareerWorx.Button.Outlined" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/primary</item>
        <item name="android:textColor">@color/primary</item>
        <item name="cornerRadius">8dp</item>
    </style>

    <!-- Card Styles -->
    <style name="Widget.CareerWorx.Card" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">2dp</item>
        <item name="strokeWidth">1dp</item>
        <item name="strokeColor">@color/card_border</item>
        <item name="cardBackgroundColor">@color/card_background</item>
    </style>

    <!-- Toolbar Style with Black Overflow Icon and White Navigation Icon -->
    <style name="Widget.CareerWorx.Toolbar" parent="Widget.MaterialComponents.Toolbar">
        <item name="android:background">@color/primary</item>
        <item name="titleTextColor">@color/white</item>
        <item name="colorControlNormal">@color/white</item>
        <item name="actionOverflowButtonStyle">@style/OverflowButtonStyle</item>
        <item name="navigationIconTint">@color/white</item>
    </style>

    <style name="OverflowButtonStyle" parent="Widget.AppCompat.ActionButton.Overflow">
        <item name="android:tint">@color/white</item>
        <item name="tint">@color/white</item>
    </style>

    <!-- Company Toolbar Style with Red Background and White Icons -->
    <style name="Widget.CareerWorx.Toolbar.Company" parent="Widget.MaterialComponents.Toolbar">
        <item name="android:background">@color/primary</item>
        <item name="titleTextColor">@color/white</item>
        <item name="colorControlNormal">@color/white</item>
        <item name="navigationIconTint">@color/white</item>
        <item name="actionOverflowButtonStyle">@style/OverflowButtonStyle</item>
    </style>
</resources>