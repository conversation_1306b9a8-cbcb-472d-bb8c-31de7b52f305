<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="CircleImageView" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

    <style name="TabTextAppearance" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">14sp</item>
        <item name="textAllCaps">false</item>
        <item name="android:textStyle">bold</item>
    </style>

    
    <style name="Widget.JobRec.Button" parent="Widget.CareerWorx.Button">
        
    </style>

    <style name="Widget.JobRec.Button.Outlined" parent="Widget.CareerWorx.Button.Outlined">
        
    </style>

    
    <style name="Widget.CareerWorx.TextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">?attr/colorPrimary</item>
        <item name="hintTextColor">?attr/colorPrimary</item>
    </style>

    <style name="Widget.CareerWorx.TextInputEditText" parent="Widget.MaterialComponents.TextInputEditText.OutlinedBox">
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textColorHint">?android:attr/textColorSecondary</item>
    </style>
</resources>
