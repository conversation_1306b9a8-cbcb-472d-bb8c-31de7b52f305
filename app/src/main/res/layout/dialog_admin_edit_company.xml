<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        android:background="@drawable/dialog_background">

        <TextView
            android:id="@+id/dialogTitleTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Edit Company"
            android:textSize="22sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="16dp" />

        
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Company Details"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    android:layout_marginBottom="8dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:hint="Company Name"
                    app:startIconDrawable="@android:drawable/ic_menu_myplaces"
                    app:startIconTint="@color/black"
                    app:boxStrokeColor="@color/black"
                    app:hintTextColor="@color/black">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/companyNameEditText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:textColor="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:hint="Industry"
                    app:startIconDrawable="@android:drawable/ic_menu_sort_by_size"
                    app:startIconTint="@color/black"
                    app:boxStrokeColor="@color/black"
                    app:hintTextColor="@color/black">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/industryEditText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:textColor="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:hint="Location"
                    app:startIconDrawable="@android:drawable/ic_menu_mylocation"
                    app:startIconTint="@color/black"
                    app:boxStrokeColor="@color/black"
                    app:hintTextColor="@color/black">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/locationEditText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:textColor="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:hint="Website"
                    app:startIconDrawable="@android:drawable/ic_menu_compass"
                    app:startIconTint="@color/black"
                    app:boxStrokeColor="@color/black"
                    app:hintTextColor="@color/black">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/websiteEditText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textUri"
                        android:textColor="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:hint="Email"
                    app:startIconDrawable="@android:drawable/ic_dialog_email"
                    app:startIconTint="@color/black"
                    app:boxStrokeColor="@color/black"
                    app:hintTextColor="@color/black">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/emailEditText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textEmailAddress"
                        android:textColor="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:hint="Company Description"
                    app:startIconDrawable="@android:drawable/ic_menu_edit"
                    app:startIconTint="@color/black"
                    app:boxStrokeColor="@color/black"
                    app:hintTextColor="@color/black"
                    app:counterEnabled="true"
                    app:counterMaxLength="500">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/descriptionEditText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:minLines="4"
                        android:gravity="top|start"
                        android:textColor="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Account Status"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    android:layout_marginBottom="8dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:hint="Status"
                    app:startIconDrawable="@android:drawable/ic_menu_agenda"
                    app:startIconTint="@color/black"
                    app:boxStrokeColor="@color/black"
                    app:hintTextColor="@color/black">

                    <AutoCompleteTextView
                        android:id="@+id/statusDropdown"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none"
                        android:textColor="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/deleteButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Delete"
                app:icon="@android:drawable/ic_menu_delete"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                app:strokeColor="@color/status_rejected"
                android:textColor="@color/status_rejected"
                app:iconTint="@color/status_rejected" />

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/cancelButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cancel"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_marginEnd="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/saveButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Save Changes"
                app:backgroundTint="@color/black" />
        </LinearLayout>
    </LinearLayout>
</androidx.core.widget.NestedScrollView>
