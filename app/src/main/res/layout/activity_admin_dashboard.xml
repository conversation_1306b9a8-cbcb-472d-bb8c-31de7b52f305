<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:colorBackground">

    
    <include
        android:id="@+id/toolbar_layout"
        layout="@layout/layout_admin_toolbar" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Admin Dashboard"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                android:layout_marginBottom="16dp"/>

            
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Overview"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                android:layout_marginBottom="12dp"/>


            
            <GridLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:columnCount="2"
                android:rowCount="2"
                android:layout_marginBottom="24dp">

                
                <androidx.cardview.widget.CardView
                    android:id="@+id/usersCard"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_columnWeight="1"
                    android:layout_rowWeight="1"
                    android:layout_margin="4dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?android:attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:background="?attr/colorPrimary">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Users"
                            android:textColor="@android:color/white"
                            android:textSize="16sp"/>

                        <TextView
                            android:id="@+id/usersCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textColor="@android:color/white"
                            android:textSize="28sp"
                            android:textStyle="bold"
                            android:layout_marginTop="8dp"/>
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                
                <androidx.cardview.widget.CardView
                    android:id="@+id/companiesCard"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_columnWeight="1"
                    android:layout_rowWeight="1"
                    android:layout_margin="4dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?android:attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:background="?attr/colorPrimary">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Companies"
                            android:textColor="@android:color/white"
                            android:textSize="16sp"/>

                        <TextView
                            android:id="@+id/companiesCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textColor="@android:color/white"
                            android:textSize="28sp"
                            android:textStyle="bold"
                            android:layout_marginTop="8dp"/>
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                
                <androidx.cardview.widget.CardView
                    android:id="@+id/jobsCard"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_columnWeight="1"
                    android:layout_rowWeight="1"
                    android:layout_margin="4dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?android:attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:background="?attr/colorPrimary">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Jobs"
                            android:textColor="@android:color/white"
                            android:textSize="16sp"/>

                        <TextView
                            android:id="@+id/jobsCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textColor="@android:color/white"
                            android:textSize="28sp"
                            android:textStyle="bold"
                            android:layout_marginTop="8dp"/>
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                
                <androidx.cardview.widget.CardView
                    android:id="@+id/applicationsCard"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_columnWeight="1"
                    android:layout_rowWeight="1"
                    android:layout_margin="4dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?android:attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:background="?attr/colorPrimary">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Applications"
                            android:textColor="@android:color/white"
                            android:textSize="16sp"/>

                        <TextView
                            android:id="@+id/applicationsCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textColor="@android:color/white"
                            android:textSize="28sp"
                            android:textStyle="bold"
                            android:layout_marginTop="8dp"/>
                    </LinearLayout>
                </androidx.cardview.widget.CardView>
            </GridLayout>

            
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Management"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                android:layout_marginBottom="16dp"/>

            
            <GridLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:columnCount="2"
                android:rowCount="2"
                android:layout_marginBottom="16dp">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_columnWeight="1"
                    android:layout_rowWeight="1"
                    android:layout_margin="4dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnManageUsers"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="Manage Users"
                        android:textColor="@android:color/white"
                        app:backgroundTint="?attr/colorPrimary"
                        app:cornerRadius="8dp"
                        android:padding="16dp"
                        android:textSize="16sp"
                        app:icon="@android:drawable/ic_menu_myplaces"
                        app:iconGravity="textStart"
                        app:iconTint="@android:color/white"/>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_columnWeight="1"
                    android:layout_rowWeight="1"
                    android:layout_margin="4dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnManageCompanies"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="Manage Companies"
                        android:textColor="@android:color/white"
                        app:backgroundTint="?attr/colorPrimary"
                        app:cornerRadius="8dp"
                        android:padding="16dp"
                        android:textSize="16sp"
                        app:icon="@android:drawable/ic_menu_compass"
                        app:iconGravity="textStart"
                        app:iconTint="@android:color/white"/>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_columnWeight="1"
                    android:layout_rowWeight="1"
                    android:layout_margin="4dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnManageJobs"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="Manage Jobs"
                        android:textColor="@android:color/white"
                        app:backgroundTint="?attr/colorPrimary"
                        app:cornerRadius="8dp"
                        android:padding="16dp"
                        android:textSize="16sp"
                        app:icon="@android:drawable/ic_menu_agenda"
                        app:iconGravity="textStart"
                        app:iconTint="@android:color/white"/>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_columnWeight="1"
                    android:layout_rowWeight="1"
                    android:layout_margin="4dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnManageApplications"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="Manage Applications"
                        android:textColor="@android:color/white"
                        app:backgroundTint="?attr/colorPrimary"
                        app:cornerRadius="8dp"
                        android:padding="16dp"
                        android:textSize="16sp"
                        app:icon="@android:drawable/ic_menu_edit"
                        app:iconGravity="textStart"
                        app:iconTint="@android:color/white"/>
                </com.google.android.material.card.MaterialCardView>
            </GridLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>