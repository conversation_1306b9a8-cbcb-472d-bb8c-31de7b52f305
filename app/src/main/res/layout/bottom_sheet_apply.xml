<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="?android:colorBackground">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Apply for Job"
        android:textSize="20sp"
        android:textStyle="bold"
        style="@style/Widget.CareerWorx.TextInputEditText" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="Choose how you would like to apply"
        android:textSize="14sp"
        android:textColor="?android:attr/textColorSecondary" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/profileCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:clickable="true"
        android:focusable="true"
        app:cardCornerRadius="12dp"
        app:cardElevation="2dp"
        app:cardBackgroundColor="?attr/colorSurface">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_profile"
                app:tint="@color/primary" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Use My Profile"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="?android:attr/textColorPrimary" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Apply using your existing profile information"
                    android:textSize="14sp"
                    android:textColor="?android:attr/textColorSecondary" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/uploadCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:clickable="true"
        android:focusable="true"
        app:cardCornerRadius="12dp"
        app:cardElevation="2dp"
        app:cardBackgroundColor="?attr/colorSurface">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_upload"
                app:tint="@color/primary" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Upload CV"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="?android:attr/textColorPrimary" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Upload your CV or resume"
                    android:textSize="14sp"
                    android:textColor="?android:attr/textColorSecondary" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</LinearLayout>