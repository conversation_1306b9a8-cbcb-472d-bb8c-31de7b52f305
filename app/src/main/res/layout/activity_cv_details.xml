<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:title="CV Details"
            app:titleTextColor="@android:color/white"
            app:navigationIcon="@drawable/ic_back" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Professional Summary">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/summaryInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textMultiLine"
                    android:minLines="3"/>

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Skills (comma-separated)">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/skillsInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"/>

            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Education"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/educationRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"/>

            <Button
                android:id="@+id/addEducationButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Add Education"
                android:layout_marginBottom="16dp"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Work Experience"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/experienceRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"/>

            <Button
                android:id="@+id/addExperienceButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Add Experience"
                android:layout_marginBottom="16dp"/>

            <Button
                android:id="@+id/submitButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Submit"
                android:layout_marginTop="16dp"/>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout> 