<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="40dp"
    android:paddingTop="4dp"
    android:paddingEnd="8dp"
    android:paddingBottom="4dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/messageCard"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="280dp"
        app:cardBackgroundColor="@color/primary"
        app:cardCornerRadius="12dp"
        app:cardElevation="1dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="10dp">

            <!-- Regular message content -->
            <TextView
                android:id="@+id/messageText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                tools:text="Hello, how are you doing today?" />

            <!-- Meeting details (visible only for meeting messages) -->
            <ScrollView
                android:id="@+id/meetingScrollView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:minHeight="200dp"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:id="@+id/meetingDetailsLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="?attr/colorSurface"
                    android:orientation="vertical"
                    android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Meeting Invitation"
                    android:textColor="?attr/colorPrimary"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/meetingDateText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:drawablePadding="6dp"
                    android:textColor="?android:attr/textColorPrimary"
                    android:textSize="16sp"
                    tools:text="Date: May 15, 2023" />

                <TextView
                    android:id="@+id/meetingTimeText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:drawablePadding="6dp"
                    android:textColor="?android:attr/textColorPrimary"
                    android:textSize="16sp"
                    tools:text="Time: 10:30 AM" />

                <TextView
                    android:id="@+id/meetingTypeText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:drawablePadding="6dp"
                    android:textColor="?android:attr/textColorPrimary"
                    android:textSize="16sp"
                    tools:text="Type: Online" />

                <TextView
                    android:id="@+id/meetingLocationText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:drawablePadding="6dp"
                    android:textColor="?android:attr/textColorPrimary"
                    android:textSize="16sp"
                    android:visibility="gone"
                    tools:text="Location: 123 Main St"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/meetingLinkText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:autoLink="web"
                    android:drawablePadding="6dp"
                    android:textColor="#1976D2"
                    android:textSize="16sp"
                    android:visibility="gone"
                    tools:text="Link: https://meet.google.com/abc-def-ghi"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/meetingStatusText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_gravity="center"
                    android:textColor="?android:attr/textColorSecondary"
                    android:textSize="16sp"
                    android:textStyle="italic"
                    tools:text="Pending response" />

                </LinearLayout>
            </ScrollView>

            <TextView
                android:id="@+id/messageTimeText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginTop="4dp"
                android:textColor="@color/white"
                android:textSize="11sp"
                tools:text="10:30 AM" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>
