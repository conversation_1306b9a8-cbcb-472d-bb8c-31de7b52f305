<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:navigationIcon="@drawable/ic_back"
            app:title="Job Details"
            app:titleTextColor="?attr/colorOnPrimary" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Header Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/jobTitle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textAppearance="?attr/textAppearanceHeadline5"
                        android:textColor="?android:attr/textColorPrimary" />

                    <TextView
                        android:id="@+id/companyName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:textAppearance="?attr/textAppearanceSubtitle1"
                        android:textColor="?android:attr/textColorSecondary" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <com.google.android.material.chip.Chip
                            android:id="@+id/jobLocation"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="?attr/textAppearanceBody2"
                            app:chipIcon="@drawable/ic_location"
                            style="@style/Widget.MaterialComponents.Chip.Action" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/jobType"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:textAppearance="?attr/textAppearanceBody2"
                            app:chipIcon="@drawable/ic_work"
                            style="@style/Widget.MaterialComponents.Chip.Action" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/jobSalary"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:drawablePadding="8dp"
                        android:textAppearance="?attr/textAppearanceBody1"
                        android:textColor="?attr/colorPrimary"
                        app:drawableStartCompat="@drawable/ic_money" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Description Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Description"
                        android:textAppearance="?attr/textAppearanceHeadline6"
                        android:textColor="?android:attr/textColorPrimary" />

                    <TextView
                        android:id="@+id/jobDescription"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:textAppearance="?attr/textAppearanceBody1"
                        android:textColor="?android:attr/textColorPrimary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Requirements Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Requirements"
                        android:textAppearance="?attr/textAppearanceHeadline6"
                        android:textColor="?android:attr/textColorPrimary" />

                    <TextView
                        android:id="@+id/jobRequirements"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:textAppearance="?attr/textAppearanceBody1"
                        android:textColor="?android:attr/textColorPrimary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <LinearLayout
                android:id="@+id/jobManagementLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal"
                android:visibility="gone">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/editJobButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="Edit Job" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/deleteJobButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="Delete Job"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton" />
            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/applyButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_margin="16dp"
        android:padding="16dp"
        android:text="Apply Now"
        android:textSize="16sp"
        app:cornerRadius="12dp"
        app:icon="@drawable/ic_send"
        app:iconGravity="textStart" />

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>