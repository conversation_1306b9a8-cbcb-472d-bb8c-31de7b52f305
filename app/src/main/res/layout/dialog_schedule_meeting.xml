<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="24dp">

        <ImageView
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:src="@drawable/ic_calendar"
            android:tint="@color/primary"
            android:layout_marginEnd="16dp"/>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Schedule Interview"
            android:textSize="22sp"
            android:textStyle="bold"
            android:textColor="@color/primary" />
    </LinearLayout>

    
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/dateInputLayout"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Date"
        app:boxStrokeColor="@color/primary"
        app:hintTextColor="@color/primary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/dateInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:inputType="none" />

    </com.google.android.material.textfield.TextInputLayout>

    
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/timeInputLayout"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:hint="Time"
        app:boxStrokeColor="@color/primary"
        app:hintTextColor="@color/primary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/timeInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:inputType="none" />

    </com.google.android.material.textfield.TextInputLayout>

    
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/durationInputLayout"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:hint="Duration"
        app:boxStrokeColor="@color/primary"
        app:hintTextColor="@color/primary">

        <AutoCompleteTextView
            android:id="@+id/durationInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="none" />

    </com.google.android.material.textfield.TextInputLayout>

    
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/meetingTypeInputLayout"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:hint="Meeting Type"
        app:boxStrokeColor="@color/primary"
        app:hintTextColor="@color/primary">

        <AutoCompleteTextView
            android:id="@+id/meetingTypeInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="none" />

    </com.google.android.material.textfield.TextInputLayout>

    
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/locationInputLayout"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:hint="Location"
        android:visibility="gone"
        app:boxStrokeColor="@color/primary"
        app:hintTextColor="@color/primary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/locationInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text" />

    </com.google.android.material.textfield.TextInputLayout>

    
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/meetingLinkInputLayout"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:hint="Meeting Link"
        android:visibility="gone"
        app:boxStrokeColor="@color/primary"
        app:hintTextColor="@color/primary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/meetingLinkInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textUri" />

    </com.google.android.material.textfield.TextInputLayout>

    
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="end"
        android:orientation="horizontal">

        <Button
            android:id="@+id/cancelButton"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:text="Cancel"
            android:textColor="@color/primary" />

        <Button
            android:id="@+id/scheduleButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/primary"
            android:text="Schedule"
            android:textColor="#FFFFFF" />

    </LinearLayout>

</LinearLayout>
