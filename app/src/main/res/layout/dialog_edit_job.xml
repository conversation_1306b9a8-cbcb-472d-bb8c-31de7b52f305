<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/editJobTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Job Title" />
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/editJobDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Job Description"
            android:inputType="textMultiLine"
            android:minLines="3" />
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/editJobRequirements"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Requirements (one per line)"
            android:inputType="textMultiLine"
            android:minLines="3" />
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/editJobSalary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Salary" />
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/editJobLocation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Location" />
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/editJobType"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Job Type" />
    </com.google.android.material.textfield.TextInputLayout>
</LinearLayout> 