<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:colorBackground"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Company Registration"
            android:textSize="24sp"
            android:textStyle="bold"
            style="@style/Widget.CareerWorx.TextInputEditText"
            android:layout_marginBottom="8dp"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Create your company account"
            android:textSize="16sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginBottom="24dp"/>

        <!-- Company Information -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            app:boxStrokeColor="@android:color/black"
            app:hintTextColor="@android:color/black">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/companyNameInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Company Name"
                style="@style/Widget.CareerWorx.TextInputEditText"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            app:boxStrokeColor="@android:color/black"
            app:hintTextColor="@android:color/black">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/registrationNumberInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Registration Number"
                style="@style/Widget.CareerWorx.TextInputEditText"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            app:boxStrokeColor="@android:color/black"
            app:hintTextColor="@android:color/black">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/industryInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Industry"
                style="@style/Widget.CareerWorx.TextInputEditText"/>
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Email and Password -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            app:boxStrokeColor="@android:color/black"
            app:hintTextColor="@android:color/black">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/emailInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Company Email"
                android:inputType="textEmailAddress"
                style="@style/Widget.CareerWorx.TextInputEditText"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            app:boxStrokeColor="@android:color/black"
            app:hintTextColor="@android:color/black"
            app:passwordToggleEnabled="true"
            app:passwordToggleTint="@android:color/black">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/passwordInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Password"
                android:inputType="textPassword"
                style="@style/Widget.CareerWorx.TextInputEditText"/>
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Register Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/registerButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Register Company"
            android:textSize="16sp"
            android:textColor="@android:color/white"
            app:backgroundTint="@color/primary"
            android:layout_marginTop="16dp"/>

    </LinearLayout>
</ScrollView>
