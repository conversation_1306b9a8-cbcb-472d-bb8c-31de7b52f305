<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/applicationCard"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="8dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    app:strokeWidth="0dp"
    android:clickable="true"
    android:focusable="true"
    app:rippleColor="@color/primary_light"
    app:cardBackgroundColor="?attr/colorSurface">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        
        <TextView
            android:id="@+id/jobTitleText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/primary"
            android:textSize="18sp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"/>

        
        <TextView
            android:id="@+id/applicantNameText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="?android:attr/textColorPrimary"
            android:textSize="16sp"
            android:maxLines="1"
            android:ellipsize="end"/>

        
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            
            <TextView
                android:id="@+id/dateText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="?android:attr/textColorSecondary"
                android:textSize="14sp"/>

            
            <com.google.android.material.chip.Chip
                android:id="@+id/statusChip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@android:color/white"
                android:textSize="12sp"
                app:chipBackgroundColor="@color/status_applied"
                app:chipMinHeight="32dp"
                app:chipMinTouchTargetSize="32dp"/>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>