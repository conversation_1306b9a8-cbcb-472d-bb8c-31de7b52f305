<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="16dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:strokeWidth="1dp"
    app:strokeColor="@color/colorOutline">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            style="@style/Widget.CareerWorx.TextInputLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etExperienceTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Job Title"
                android:textColor="@color/black"
                style="@style/Widget.CareerWorx.TextInputEditText"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            style="@style/Widget.CareerWorx.TextInputLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etExperienceCompany"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Company"
                android:textColor="@color/black"
                style="@style/Widget.CareerWorx.TextInputEditText"/>
        </com.google.android.material.textfield.TextInputLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                style="@style/Widget.CareerWorx.TextInputLayout">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etExperienceStartDate"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Start Date"
                    android:textColor="@color/black"
                    style="@style/Widget.CareerWorx.TextInputEditText"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                style="@style/Widget.CareerWorx.TextInputLayout">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etExperienceEndDate"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="End Date"
                    android:textColor="@color/black"
                    style="@style/Widget.CareerWorx.TextInputEditText"/>
            </com.google.android.material.textfield.TextInputLayout>
        </LinearLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            style="@style/Widget.CareerWorx.TextInputLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etExperienceDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Description"
                android:inputType="textMultiLine"
                android:minLines="2"
                android:gravity="top"
                android:textColor="@color/black"
                style="@style/Widget.CareerWorx.TextInputEditText"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnRemoveExperience"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Remove"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:textColor="?android:attr/textColorPrimary"/>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>