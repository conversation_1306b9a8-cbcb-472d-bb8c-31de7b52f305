<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/searchEditText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Enter name or job title"
        android:inputType="text"
        android:maxLines="1"
        android:layout_marginBottom="16dp"
        android:textColor="?android:attr/textColorPrimary"
        android:textColorHint="?android:attr/textColorSecondary" />

    <Button
        android:id="@+id/searchButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Search"
        android:backgroundTint="@color/primary"
        android:textColor="@color/white" />

</LinearLayout>
