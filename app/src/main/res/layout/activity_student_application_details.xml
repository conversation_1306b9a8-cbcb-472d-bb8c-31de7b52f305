<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:colorBackground">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            style="@style/Widget.CareerWorx.Toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="Application Details" />

    </com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/buttonContainer"
        app:layout_constraintTop_toBottomOf="@id/appBarLayout">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Status Banner -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Application Status"
                            android:textAppearance="?attr/textAppearanceHeadline6"
                            android:textColor="?android:attr/textColorPrimary"
                            android:textStyle="bold" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="1dp"
                            android:layout_weight="1" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/statusChip"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="?attr/colorOnPrimary"
                            app:chipBackgroundColor="@color/status_pending"
                            tools:text="Pending" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/statusDescriptionText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:textAppearance="?attr/textAppearanceBody2"
                        android:textColor="@color/text_secondary"
                        tools:text="Your application is being reviewed by the employer." />

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Job Details Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Job Details"
                        android:textAppearance="?attr/textAppearanceHeadline6"
                        android:textColor="?attr/colorPrimary"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_work"
                            app:tint="?android:attr/textColorPrimary" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Job Title"
                                android:textAppearance="?attr/textAppearanceCaption"
                                android:textColor="@color/text_secondary" />

                            <TextView
                                android:id="@+id/jobTitleText"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textAppearance="?attr/textAppearanceBody1"
                                android:textColor="@color/text_primary"
                                android:textStyle="bold"
                                tools:text="Software Engineer" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_business"
                            app:tint="?android:attr/textColorPrimary" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Company"
                                android:textAppearance="?attr/textAppearanceCaption"
                                android:textColor="@color/text_secondary" />

                            <TextView
                                android:id="@+id/companyNameText"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textAppearance="?attr/textAppearanceBody1"
                                android:textColor="@color/text_primary"
                                tools:text="Google" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_location"
                            app:tint="?android:attr/textColorPrimary" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Location"
                                android:textAppearance="?attr/textAppearanceCaption"
                                android:textColor="@color/text_secondary" />

                            <TextView
                                android:id="@+id/jobLocationText"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textAppearance="?attr/textAppearanceBody1"
                                android:textColor="@color/text_primary"
                                tools:text="Mountain View, CA" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/jobTypeContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_schedule"
                            app:tint="?android:attr/textColorPrimary" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Job Type"
                                android:textAppearance="?attr/textAppearanceCaption"
                                android:textColor="@color/text_secondary" />

                            <TextView
                                android:id="@+id/jobTypeText"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textAppearance="?attr/textAppearanceBody1"
                                android:textColor="@color/text_primary"
                                tools:text="Full-time" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/salaryContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_attach_money"
                            app:tint="?android:attr/textColorPrimary" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Salary"
                                android:textAppearance="?attr/textAppearanceCaption"
                                android:textColor="@color/text_secondary" />

                            <TextView
                                android:id="@+id/salaryText"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textAppearance="?attr/textAppearanceBody1"
                                android:textColor="@color/text_primary"
                                tools:text="$100,000 - $150,000 per year" />
                        </LinearLayout>
                    </LinearLayout>

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Application Details Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Application Timeline"
                        android:textAppearance="?attr/textAppearanceHeadline6"
                        android:textColor="?attr/colorPrimary"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_calendar"
                            app:tint="?android:attr/textColorPrimary" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Applied Date"
                                android:textAppearance="?attr/textAppearanceCaption"
                                android:textColor="@color/text_secondary" />

                            <TextView
                                android:id="@+id/appliedDateText"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textAppearance="?attr/textAppearanceBody1"
                                android:textColor="@color/text_primary"
                                tools:text="Jan 15, 2023" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_update"
                            app:tint="?android:attr/textColorPrimary" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Last Updated"
                                android:textAppearance="?attr/textAppearanceCaption"
                                android:textColor="@color/text_secondary" />

                            <TextView
                                android:id="@+id/lastUpdatedText"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:textAppearance="?attr/textAppearanceBody1"
                                android:textColor="@color/text_primary"
                                tools:text="Feb 1, 2023" />
                        </LinearLayout>
                    </LinearLayout>

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>
    </ScrollView>

    <!-- Button Container -->
    <LinearLayout
        android:id="@+id/buttonContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?android:colorBackground"
        android:elevation="8dp"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <Button
            android:id="@+id/viewJobDetailsButton"
            style="@style/Widget.JobRec.Button.Outlined"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="View Full Job Details" />

        <Button
            android:id="@+id/chatButton"
            style="@style/Widget.JobRec.Button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Chat with Employer"
            android:visibility="gone"
            tools:visibility="visible" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>