<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/jobCard"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="8dp"
    style="@style/Widget.CareerWorx.Card">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/jobTitleText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            android:maxLines="2"
            android:ellipsize="end"/>

        <TextView
            android:id="@+id/companyNameText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            android:maxLines="1"
            android:ellipsize="end"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/locationText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/ic_location"
                android:drawablePadding="4dp"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:maxLines="1"
                android:ellipsize="end"/>

            <TextView
                android:id="@+id/salaryText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:drawableStart="@drawable/ic_money"
                android:drawablePadding="4dp"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:maxLines="1"
                android:ellipsize="end"/>

        </LinearLayout>

        <TextView
            android:id="@+id/jobTypeText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/bg_job_type"
            android:paddingHorizontal="12dp"
            android:paddingVertical="4dp"
            android:textColor="@color/primary"
            android:textSize="12sp"/>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>