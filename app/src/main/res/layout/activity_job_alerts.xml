<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:title="Job Alerts"
            app:titleTextColor="@android:color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Notification Preferences"
                        android:textAppearance="?attr/textAppearanceHeadline6" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/emailNotificationsSwitch"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="Email Notifications" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/pushNotificationsSwitch"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="Push Notifications" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/notificationFrequencyLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:hint="Notification Frequency">

                        <AutoCompleteTextView
                            android:id="@+id/notificationFrequencyDropdown"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="Active Job Alerts"
                android:textAppearance="?attr/textAppearanceHeadline6" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/jobAlertsRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/addJobAlertFab"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end|bottom"
                android:layout_margin="16dp"
                android:contentDescription="Add Job Alert"
                app:srcCompat="@android:drawable/ic_input_add" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout> 