<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/notificationCard"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/notificationTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toStartOf="@+id/deleteButton"
                android:text="Notification Title"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@android:color/white" />

            <ImageButton
                android:id="@+id/deleteButton"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@android:drawable/ic_menu_delete"
                android:tint="@color/primary"
                android:contentDescription="Delete notification" />
        </RelativeLayout>

        <TextView
            android:id="@+id/notificationMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="Notification message goes here"
            android:textSize="14sp"
            android:textColor="@android:color/white" />

        <TextView
            android:id="@+id/notificationTime"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Jan 1, 2023 at 12:00 PM"
            android:textSize="12sp"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/applyButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Apply Now"
            android:textSize="12sp"
            android:backgroundTint="@color/primary"
            android:visibility="gone" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
