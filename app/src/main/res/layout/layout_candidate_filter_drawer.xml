<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="280dp"
    android:layout_height="match_parent"
    android:layout_gravity="end"
    android:background="@color/background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Filter Candidates"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="16dp"/>

        
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/skillsFilterLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="Skills">

            <AutoCompleteTextView
                android:id="@+id/skillsFilter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none" />
        </com.google.android.material.textfield.TextInputLayout>

        
        <com.google.android.material.chip.ChipGroup
            android:id="@+id/selectedSkillsChipGroup"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp" />

        
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/educationFilterLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="Education Level">

            <AutoCompleteTextView
                android:id="@+id/educationFilter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none" />
        </com.google.android.material.textfield.TextInputLayout>

        
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/experienceFilterLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="Experience Level">

            <AutoCompleteTextView
                android:id="@+id/experienceFilter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none" />
        </com.google.android.material.textfield.TextInputLayout>

        
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/locationFilterLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="Location">

            <AutoCompleteTextView
                android:id="@+id/locationFilter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none" />
        </com.google.android.material.textfield.TextInputLayout>

        
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/fieldFilterLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="Field/Industry">

            <AutoCompleteTextView
                android:id="@+id/fieldFilter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none" />
        </com.google.android.material.textfield.TextInputLayout>

        
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/subFieldFilterLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="Specialization">

            <AutoCompleteTextView
                android:id="@+id/subFieldFilter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none" />
        </com.google.android.material.textfield.TextInputLayout>

        
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp">

            
            <com.google.android.material.button.MaterialButton
                android:id="@+id/clearFiltersButton"
                style="@style/Widget.JobRec.Button.Outlined"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="Clear" />

            
            <com.google.android.material.button.MaterialButton
                android:id="@+id/applyFiltersButton"
                style="@style/Widget.JobRec.Button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Apply"
                app:backgroundTint="@color/primary" />
        </LinearLayout>
    </LinearLayout>
</ScrollView>
