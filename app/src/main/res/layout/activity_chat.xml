<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:colorBackground">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            style="@style/Widget.CareerWorx.Toolbar"
            app:titleTextColor="@color/white"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- Chat Header -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/chatHeader"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/primary"
            android:padding="12dp"
            app:layout_constraintTop_toTopOf="parent">

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/participantImageView"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_company_placeholder"
                app:civ_border_color="#E0E0E0"
                app:civ_border_width="1dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/participantImageView"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/participantNameText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="Samsung Electronics" />

                <TextView
                    android:id="@+id/jobTitleText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    tools:text="Senior Software Engineer" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Background Pattern with white background -->
        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="?attr/colorSurface"
            app:layout_constraintBottom_toTopOf="@id/messageInputLayout"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Messages RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/messagesRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:clipToPadding="false"
            android:padding="8dp"
            app:layout_constraintBottom_toTopOf="@id/messageInputLayout"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Empty State View -->
        <TextView
            android:id="@+id/emptyView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No messages yet. Start the conversation!"
            android:textColor="?android:attr/textColorSecondary"
            android:textSize="16sp"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/messageInputLayout"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Message Input Layout - WhatsApp style -->
        <LinearLayout
            android:id="@+id/messageInputLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/colorSurface"
            android:orientation="horizontal"
            android:padding="8dp"
            app:layout_constraintBottom_toBottomOf="parent">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:cardCornerRadius="24dp"
                app:cardElevation="0dp"
                app:strokeWidth="1dp"
                app:strokeColor="?attr/colorOutline">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/messageInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:hint="Type a message"
                    android:inputType="textMultiLine"
                    android:maxLines="4"
                    android:padding="12dp"
                    android:textColor="?android:attr/textColorPrimary"
                    android:textColorHint="?android:attr/textColorSecondary"
                    android:textSize="16sp" />

            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/sendButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_gravity="bottom"
                android:layout_marginStart="8dp"
                android:backgroundTint="@color/primary"
                android:contentDescription="Send message"
                app:borderWidth="0dp"
                app:fabCustomSize="48dp"
                app:srcCompat="@android:drawable/ic_menu_send"
                app:tint="@color/white" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>



</androidx.coordinatorlayout.widget.CoordinatorLayout>
