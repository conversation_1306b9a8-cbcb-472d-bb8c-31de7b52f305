<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="280dp"
    android:layout_height="match_parent"
    android:layout_gravity="end"
    android:background="?android:colorBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Filter Jobs"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:layout_marginBottom="16dp"/>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/jobFieldLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Job Field">

            <AutoCompleteTextView
                android:id="@+id/jobFieldDropdown"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none"/>

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/jobSpecializationLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Specialization">

            <AutoCompleteTextView
                android:id="@+id/jobSpecializationDropdown"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none"/>

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/provinceLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Province">

            <AutoCompleteTextView
                android:id="@+id/provinceDropdown"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none"/>

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/salaryRangeLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Salary Range">

            <AutoCompleteTextView
                android:id="@+id/salaryRangeDropdown"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none"/>

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/experienceLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Experience Level">

            <AutoCompleteTextView
                android:id="@+id/experienceDropdown"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none"/>

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Job Type Filter -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Job Type"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:layout_marginBottom="8dp"/>

        <com.google.android.material.chip.ChipGroup
            android:id="@+id/jobTypeChipGroup"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:singleSelection="false">

            <com.google.android.material.chip.Chip
                android:id="@+id/chipFullTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Full-time"
                android:checkable="true"
                app:chipBackgroundColor="@color/chip_background_color"
                app:chipStrokeColor="@color/primary"
                app:chipStrokeWidth="1dp"
                android:textColor="@color/chip_text_color"/>

            <com.google.android.material.chip.Chip
                android:id="@+id/chipPartTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Part-time"
                android:checkable="true"
                app:chipBackgroundColor="@color/chip_background_color"
                app:chipStrokeColor="@color/primary"
                app:chipStrokeWidth="1dp"
                android:textColor="@color/chip_text_color"/>

            <com.google.android.material.chip.Chip
                android:id="@+id/chipContract"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Contract"
                android:checkable="true"
                app:chipBackgroundColor="@color/chip_background_color"
                app:chipStrokeColor="@color/primary"
                app:chipStrokeWidth="1dp"
                android:textColor="@color/chip_text_color"/>

            <com.google.android.material.chip.Chip
                android:id="@+id/chipRemote"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Remote"
                android:checkable="true"
                app:chipBackgroundColor="@color/chip_background_color"
                app:chipStrokeColor="@color/primary"
                app:chipStrokeWidth="1dp"
                android:textColor="@color/chip_text_color"/>
        </com.google.android.material.chip.ChipGroup>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/clearFiltersButton"
                style="@style/Widget.JobRec.Button.Outlined"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Clear"
                android:layout_marginEnd="8dp"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/applyFiltersButton"
                style="@style/Widget.JobRec.Button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Apply"
                app:backgroundTint="@color/primary"/>
        </LinearLayout>

    </LinearLayout>
</ScrollView>
