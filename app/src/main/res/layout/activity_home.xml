<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:colorBackground">

    
    <include
        android:id="@+id/toolbar_layout"
        layout="@layout/toolbar_main" />

    
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="72dp">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="56dp"
            android:clipToPadding="false">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginBottom="24dp">

                    <TextView
                        android:id="@+id/welcomeText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Welcome back!"
                        android:textSize="28sp"
                        android:textStyle="bold"
                        android:textColor="?attr/colorPrimary"/>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/returnToCompanyView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Return to Company View"
                        android:layout_marginTop="8dp"
                        android:visibility="gone"
                        app:backgroundTint="@color/primary"
                        app:cornerRadius="8dp"/>
                </LinearLayout>

                
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/searchCard"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    app:strokeWidth="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical"
                        android:background="#FFFFFF">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_search"
                            android:tint="@color/black"
                            android:layout_marginEnd="12dp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Search jobs..."
                            android:textColor="#757575"
                            android:textSize="16sp" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="24dp">

                    
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="4dp"
                        app:strokeWidth="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="20dp"
                            android:background="@color/primary">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Applications"
                                android:textColor="@android:color/white"
                                android:textSize="14sp"/>

                            <TextView
                                android:id="@+id/applicationsCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textColor="@android:color/white"
                                android:textSize="32sp"
                                android:textStyle="bold"
                                android:layout_marginTop="8dp"/>
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                    
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/savedJobsCard"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="4dp"
                        app:strokeWidth="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="20dp"
                            android:background="@color/primary">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Saved Jobs"
                                android:textColor="@android:color/white"
                                android:textSize="14sp"/>

                            <TextView
                                android:id="@+id/savedJobsCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textColor="@android:color/white"
                                android:textSize="32sp"
                                android:textStyle="bold"
                                android:layout_marginTop="8dp"/>
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>
                </LinearLayout>

                
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Quick Actions"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="24dp">

                    
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/myApplicationsCard"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="4dp"
                        app:strokeWidth="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="20dp"
                            android:background="#FFFFFF">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:src="@drawable/ic_application"
                                android:tint="@color/black"
                                android:layout_marginBottom="12dp"/>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="My Applications"
                                android:textSize="16sp"
                                android:textColor="@color/primary"
                                android:gravity="center"/>
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                    
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/jobAlertsCard"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="4dp"
                        app:strokeWidth="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="20dp"
                            android:background="#FFFFFF">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:src="@android:drawable/ic_dialog_email"
                                android:tint="@color/black"
                                android:layout_marginBottom="12dp"/>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Messages"
                                android:textSize="16sp"
                                android:textColor="@color/primary"
                                android:gravity="center"/>
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>
                </LinearLayout>

                
                <TextView
                    android:id="@+id/recentJobsLabel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Recently Posted Jobs"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recentJobsRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipToPadding="false"
                    android:orientation="horizontal"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    android:layout_marginBottom="24dp"/>

                
                <TextView
                    android:id="@+id/recommendedJobsLabel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Recommended for You"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="16dp"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recommendedJobsRecyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipToPadding="false"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottomNavigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="?android:colorBackground"
        app:menu="@menu/bottom_nav_menu"
        app:labelVisibilityMode="labeled"
        app:itemIconTint="@color/student_bottom_nav_item_color"
        app:itemTextColor="@color/student_bottom_nav_item_color"
        app:elevation="8dp"/>

</androidx.coordinatorlayout.widget.CoordinatorLayout>