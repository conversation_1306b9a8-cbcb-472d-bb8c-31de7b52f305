<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@color/background"
    android:gravity="center">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Welcome to CareerWorx!"
        android:textSize="32sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:layout_marginBottom="8dp"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Sign in to continue"
        android:textSize="16sp"
        android:textColor="@color/text_secondary"
        android:layout_marginBottom="16dp"/>

    <!-- User Type Selector -->
    <RadioGroup
        android:id="@+id/userTypeRadioGroup"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginBottom="16dp">

        <RadioButton
            android:id="@+id/studentRadioButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Student"
            android:checked="true"
            android:layout_marginEnd="16dp"/>

        <RadioButton
            android:id="@+id/companyRadioButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Company"
            android:layout_marginEnd="16dp"/>

        <RadioButton
            android:id="@+id/adminRadioButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Admin"/>
    </RadioGroup>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        app:boxStrokeColor="@color/primary"
        app:hintTextColor="@color/primary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/emailInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Email"
            android:inputType="textEmailAddress"
            android:textColor="@color/text_primary"/>
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        app:boxStrokeColor="@color/primary"
        app:hintTextColor="@color/primary"
        app:passwordToggleEnabled="true"
        app:passwordToggleTint="@color/primary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/passwordInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Password"
            android:inputType="textPassword"
            android:textColor="@color/text_primary"/>
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/loginButton"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:text="Sign In"
        android:textSize="16sp"
        android:textColor="@color/white"
        app:backgroundTint="@color/primary"
        style="@style/Widget.JobRec.Button"
        android:layout_marginBottom="24dp"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/signupButton"
            style="@style/Widget.JobRec.Button.Outlined"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Don't have an account? Sign up"
            android:textColor="@color/primary"
            android:textSize="14sp"
            app:strokeColor="@color/primary"
            app:strokeWidth="1dp"
            android:layout_marginBottom="16dp"/>

        <!-- Debug button - hidden by default -->
    </LinearLayout>

</LinearLayout>