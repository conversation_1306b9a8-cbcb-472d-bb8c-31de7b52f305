<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Search"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:layout_marginBottom="8dp"/>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/searchInputLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:hint="Search by keyword"
            app:startIconDrawable="@android:drawable/ic_menu_search"
            app:startIconTint="#FF0000"
            app:boxStrokeColor="#FF0000"
            app:hintTextColor="#FF0000">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/searchEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:imeOptions="actionSearch"
                android:maxLines="1"
                android:textColor="@android:color/white"/>
        </com.google.android.material.textfield.TextInputLayout>

        <LinearLayout
            android:id="@+id/filterContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/filterButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Filters"
                android:textColor="#FFFFFF"
                app:backgroundTint="#FF0000"
                app:cornerRadius="4dp"
                app:icon="@android:drawable/ic_menu_sort_by_size"
                app:iconTint="#FFFFFF"
                style="@style/Widget.MaterialComponents.Button.UnelevatedButton"/>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/searchButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Search"
                android:textColor="#FFFFFF"
                app:backgroundTint="#FF0000"
                app:cornerRadius="4dp"
                style="@style/Widget.MaterialComponents.Button.UnelevatedButton"/>
        </LinearLayout>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>
