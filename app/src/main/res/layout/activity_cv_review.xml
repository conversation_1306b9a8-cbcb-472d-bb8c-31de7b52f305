<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Profile Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/profileImage"
                        android:layout_width="120dp"
                        android:layout_height="120dp"
                        android:layout_gravity="center"
                        android:layout_marginBottom="16dp"
                        android:scaleType="centerCrop"
                        app:shapeAppearanceOverlay="@style/CircleImageView" />

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/nameText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:textAppearance="?attr/textAppearanceHeadline6" />

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/emailText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:textAppearance="?attr/textAppearanceBody1" />

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/phoneText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:textAppearance="?attr/textAppearanceBody1" />

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/locationText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:textAppearance="?attr/textAppearanceBody1" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Education Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/educationCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="Education"
                        android:textAppearance="?attr/textAppearanceHeadline6" />

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/educationText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textAppearance="?attr/textAppearanceBody1" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Experience Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/experienceCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="Experience"
                        android:textAppearance="?attr/textAppearanceHeadline6" />

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/experienceText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textAppearance="?attr/textAppearanceBody1" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Skills Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/skillsCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="Skills"
                        android:textAppearance="?attr/textAppearanceHeadline6" />

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/skillsText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textAppearance="?attr/textAppearanceBody1" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Languages Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/languagesCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <com.google.android.material.textview.MaterialTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="Languages"
                        android:textAppearance="?attr/textAppearanceHeadline6" />

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/languagesText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textAppearance="?attr/textAppearanceBody1" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Contact Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/contactButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="Contact Candidate"
                app:cornerRadius="8dp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
