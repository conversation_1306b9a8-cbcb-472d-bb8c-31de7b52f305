<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/profileImage"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:scaleType="centerCrop"
                app:shapeAppearanceOverlay="@style/CircleImageView"
                android:layout_marginEnd="16dp"/>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/nameText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="?android:attr/textColorPrimary"
                    android:layout_marginBottom="4dp"/>

                <TextView
                    android:id="@+id/educationText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:textColor="?android:attr/textColorSecondary"
                    android:layout_marginBottom="4dp"/>

                <TextView
                    android:id="@+id/locationText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:textColor="?android:attr/textColorSecondary"
                    android:layout_marginBottom="4dp"/>
            </LinearLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/viewProfileButton"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="View Profile"
                android:textColor="@color/primary" />
        </LinearLayout>

        <TextView
            android:id="@+id/skillsText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="?android:attr/textColorSecondary"
            android:layout_marginTop="8dp"/>

        <TextView
            android:id="@+id/experienceText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="?android:attr/textColorSecondary"
            android:layout_marginTop="4dp"/>

    </LinearLayout>
</com.google.android.material.card.MaterialCardView>