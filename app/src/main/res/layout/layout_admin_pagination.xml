<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center"
    android:padding="8dp"
    android:background="?android:colorBackground">

    <TextView
        android:id="@+id/paginationInfoText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Page 1 of 1 (0 items)"
        android:textColor="?android:attr/textColorPrimary"
        android:layout_marginEnd="16dp"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/prevPageButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Previous"
        android:textColor="#FFFFFF"
        app:backgroundTint="#FF0000"
        app:cornerRadius="4dp"
        android:layout_marginEnd="8dp"
        style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
        android:minWidth="0dp"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/nextPageButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Next"
        android:textColor="#FFFFFF"
        app:backgroundTint="#FF0000"
        app:cornerRadius="4dp"
        style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
        android:minWidth="0dp"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"/>

</LinearLayout>
