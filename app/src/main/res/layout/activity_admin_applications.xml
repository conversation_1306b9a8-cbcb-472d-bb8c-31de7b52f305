<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:colorBackground">

    <!-- Include the admin toolbar -->
    <include
        android:id="@+id/toolbar_layout"
        layout="@layout/layout_admin_toolbar" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- Include search layout -->
        <include
            android:id="@+id/search_layout"
            layout="@layout/layout_admin_search" />

        <!-- Main content -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:padding="8dp" />

            <LinearLayout
                android:id="@+id/emptyStateLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:src="@android:drawable/ic_menu_search"
                    app:tint="?android:attr/textColorSecondary"
                    android:contentDescription="No applications found"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="No applications found"
                    android:textColor="?android:attr/textColorPrimary"
                    android:textSize="18sp"
                    android:textStyle="bold"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Try adjusting your search or check back later"
                    android:textAlignment="center"
                    android:textColor="?android:attr/textColorSecondary"/>
            </LinearLayout>

            <com.google.android.material.progressindicator.CircularProgressIndicator
                android:id="@+id/progressIndicator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone" />
        </FrameLayout>

        <!-- Include pagination layout -->
        <include
            android:id="@+id/pagination_layout"
            layout="@layout/layout_admin_pagination" />
    </LinearLayout>

    <!-- FAB for adding new application -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/addApplicationFab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="32dp"
        android:src="@android:drawable/ic_input_add"
        app:backgroundTint="#FF0000"
        app:tint="?attr/colorOnPrimary" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>