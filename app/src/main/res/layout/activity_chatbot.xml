<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:colorBackground">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            style="@style/Widget.CareerWorx.Toolbar"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:title="CareerWorx Assistant" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/messagesRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:clipToPadding="false"
            android:padding="8dp"
            app:layout_constraintBottom_toTopOf="@id/inputLayout"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/emptyView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="16dp"
            android:text="Ask me anything about CareerWorx! I'm here to help."
            android:textColor="?android:attr/textColorPrimary"
            android:textSize="16sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/inputLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?android:colorBackground"
            android:orientation="horizontal"
            android:padding="8dp"
            app:layout_constraintBottom_toBottomOf="parent">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/messageInputLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:hint="Type your question...">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/messageInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textMultiLine"
                    android:maxLines="4" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/sendButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:backgroundTint="?attr/colorPrimary"
                android:contentDescription="Send message"
                app:tint="?attr/colorOnPrimary"
                app:srcCompat="@android:drawable/ic_menu_send" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
