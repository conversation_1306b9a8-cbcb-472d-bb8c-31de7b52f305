<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary"
        app:elevation="4dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            style="@style/Widget.CareerWorx.Toolbar"
            app:title="Analytics Dashboard" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Applications Overview -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="4dp"
                app:cardCornerRadius="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Applications Overview"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp"/>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            app:cardCornerRadius="8dp"
                            app:cardElevation="2dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:padding="16dp"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/pendingApplicationsText"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    android:text="0"/>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Pending"
                                    android:textSize="14sp"/>

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            app:cardCornerRadius="8dp"
                            app:cardElevation="2dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:padding="16dp"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/acceptedApplicationsText"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    android:text="0"/>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Accepted"
                                    android:textSize="14sp"/>

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            app:cardCornerRadius="8dp"
                            app:cardElevation="2dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:padding="16dp"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/rejectedApplicationsText"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    android:text="0"/>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Rejected"
                                    android:textSize="14sp"/>

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            app:cardCornerRadius="8dp"
                            app:cardElevation="2dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:padding="16dp"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/totalApplicationsText"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    android:text="0"/>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Total"
                                    android:textSize="14sp"/>

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Jobs Overview -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardElevation="4dp"
                app:cardCornerRadius="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Jobs Overview"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp"/>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            app:cardCornerRadius="8dp"
                            app:cardElevation="2dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:padding="16dp"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/activeJobsText"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    android:text="0"/>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Active Jobs"
                                    android:textSize="14sp"/>

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            app:cardCornerRadius="8dp"
                            app:cardElevation="2dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:padding="16dp"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/totalJobsText"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    android:text="0"/>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Total Jobs"
                                    android:textSize="14sp"/>

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Recent Activity -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardElevation="4dp"
                app:cardCornerRadius="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Recent Activity"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp"/>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recentActivityRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>