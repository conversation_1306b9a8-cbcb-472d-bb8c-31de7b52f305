package com.example.jobrec.services

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.media.RingtoneManager
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import com.example.jobrec.HomeActivity
import com.example.jobrec.R
import com.example.jobrec.ChatActivity
import com.example.jobrec.JobDetailsActivity
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.google.firebase.firestore.FirebaseFirestore

class FCMService : FirebaseMessagingService() {
    private val TAG = "FCMService"

    companion object {
        const val CHANNEL_ID_JOBS = "job_notifications"
        const val CHANNEL_ID_MESSAGES = "message_notifications"
        const val CHANNEL_NAME_JOBS = "Job Notifications"
        const val CHANNEL_NAME_MESSAGES = "Message Notifications"
    }

    /**
     * Called when a new token is generated for the app
     */
    override fun onNewToken(token: String) {
        Log.d(TAG, "Refreshed token: $token")

        // Save the token to Firestore for the current user
        saveTokenToFirestore(token)
    }

    /**
     * Called when a message is received
     */
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        Log.d(TAG, "From: ${remoteMessage.from}")

        // Always show notification regardless of app state (foreground or background)
        // This ensures notifications are shown even when the app is in the foreground

        // Check if message contains a data payload
        if (remoteMessage.data.isNotEmpty()) {
            Log.d(TAG, "Message data payload: ${remoteMessage.data}")

            // Handle the data payload based on notification type
            val notificationType = remoteMessage.data["type"] ?: "general"

            when (notificationType) {
                "job" -> handleJobNotification(remoteMessage.data)
                "message" -> handleMessageNotification(remoteMessage.data)
                else -> handleGeneralNotification(remoteMessage.data)
            }
        }

        // Check if message contains a notification payload
        remoteMessage.notification?.let {
            Log.d(TAG, "Message Notification Body: ${it.body}")

            // Always show notification payload, even if there's also data
            // This ensures notifications are shown in all cases
            val title = it.title ?: "CareerWorx"
            val body = it.body ?: "You have a new notification"

            // Create an intent based on data if available
            if (remoteMessage.data.isNotEmpty()) {
                val notificationType = remoteMessage.data["type"] ?: "general"

                when (notificationType) {
                    "job" -> {
                        val jobId = remoteMessage.data["jobId"]
                        if (jobId != null) {
                            val intent = Intent(this, JobDetailsActivity::class.java).apply {
                                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                                putExtra("jobId", jobId)
                            }
                            sendNotification(title, body, intent, CHANNEL_ID_JOBS)
                        } else {
                            sendNotification(title, body, null, CHANNEL_ID_JOBS)
                        }
                    }
                    "message" -> {
                        val conversationId = remoteMessage.data["conversationId"]
                        if (conversationId != null) {
                            val intent = Intent(this, ChatActivity::class.java).apply {
                                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                                putExtra("conversationId", conversationId)
                            }
                            sendNotification(title, body, intent, CHANNEL_ID_MESSAGES)
                        } else {
                            sendNotification(title, body, null, CHANNEL_ID_MESSAGES)
                        }
                    }
                    else -> sendNotification(title, body, null, null)
                }
            } else {
                // If there's no data, use a general notification
                sendNotification(title, body, null, null)
            }
        }
    }

    /**
     * Handle job-related notifications
     */
    private fun handleJobNotification(data: Map<String, String>) {
        val title = data["title"] ?: "New Job Posting"
        val body = data["body"] ?: "A new job has been posted that matches your profile"
        val jobId = data["jobId"]

        // Create an intent to open the JobDetailsActivity
        val intent = Intent(this, JobDetailsActivity::class.java).apply {
            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            putExtra("jobId", jobId)
        }

        sendNotification(title, body, intent, CHANNEL_ID_JOBS)
    }

    /**
     * Handle message-related notifications
     */
    private fun handleMessageNotification(data: Map<String, String>) {
        val title = data["title"] ?: "New Message"
        val body = data["body"] ?: "You have received a new message"
        val conversationId = data["conversationId"]

        // Create an intent to open the ChatActivity
        val intent = Intent(this, ChatActivity::class.java).apply {
            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            putExtra("conversationId", conversationId)
        }

        sendNotification(title, body, intent, CHANNEL_ID_MESSAGES)
    }

    /**
     * Handle general notifications
     */
    private fun handleGeneralNotification(data: Map<String, String>) {
        val title = data["title"] ?: "CareerWorx"
        val body = data["body"] ?: "You have a new notification"

        // Create an intent to open the MainActivity
        val intent = Intent(this, HomeActivity::class.java).apply {
            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        }

        sendNotification(title, body, intent, null)
    }

    /**
     * Create and show a notification
     * This will display notifications even when the app is in the foreground
     */
    private fun sendNotification(title: String, messageBody: String, intent: Intent?, channelId: String?) {
        val pendingIntent = if (intent != null) {
            PendingIntent.getActivity(
                this, 0, intent,
                PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
            )
        } else {
            // Default intent to open the app
            val defaultIntent = Intent(this, HomeActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            }
            PendingIntent.getActivity(
                this, 0, defaultIntent,
                PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
            )
        }

        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        val notificationBuilder = NotificationCompat.Builder(this, channelId ?: CHANNEL_ID_JOBS)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(messageBody)
            .setAutoCancel(true)
            .setSound(defaultSoundUri)
            .setContentIntent(pendingIntent)
            // Set high priority to ensure it shows as a heads-up notification
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            // Add these flags to ensure the notification is shown in foreground
            .setDefaults(NotificationCompat.DEFAULT_ALL)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        // Create notification channels for Android O and above
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val jobChannel = NotificationChannel(
                CHANNEL_ID_JOBS,
                CHANNEL_NAME_JOBS,
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for new job postings"
                enableLights(true)
                enableVibration(true)
                // Set lockscreen visibility
                lockscreenVisibility = NotificationCompat.VISIBILITY_PUBLIC
                // Enable badge
                setShowBadge(true)
                // Set sound
                setSound(defaultSoundUri, null)
            }

            val messageChannel = NotificationChannel(
                CHANNEL_ID_MESSAGES,
                CHANNEL_NAME_MESSAGES,
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for new messages"
                enableLights(true)
                enableVibration(true)
                // Set lockscreen visibility
                lockscreenVisibility = NotificationCompat.VISIBILITY_PUBLIC
                // Enable badge
                setShowBadge(true)
                // Set sound
                setSound(defaultSoundUri, null)
            }

            notificationManager.createNotificationChannel(jobChannel)
            notificationManager.createNotificationChannel(messageChannel)
        }

        // Generate a unique notification ID
        val notificationId = System.currentTimeMillis().toInt()
        notificationManager.notify(notificationId, notificationBuilder.build())
    }

    /**
     * Save the FCM token to Firestore for the current user
     */
    private fun saveTokenToFirestore(token: String) {
        val auth = com.google.firebase.auth.FirebaseAuth.getInstance()
        val currentUser = auth.currentUser

        if (currentUser != null) {
            val userId = currentUser.uid
            val db = FirebaseFirestore.getInstance()

            // Save token to user document
            db.collection("users").document(userId)
                .update("fcmToken", token)
                .addOnSuccessListener {
                    Log.d(TAG, "FCM Token saved for user: $userId")
                }
                .addOnFailureListener { e ->
                    Log.e(TAG, "Error saving FCM token", e)

                    // If update fails (document might not exist), try to check if it's a company
                    checkAndSaveCompanyToken(userId, token)
                }
        } else {
            Log.d(TAG, "User not logged in, token not saved")
        }
    }

    /**
     * Check if the user is a company and save the token to the company document
     */
    private fun checkAndSaveCompanyToken(userId: String, token: String) {
        val db = FirebaseFirestore.getInstance()

        // Check if user is a company
        db.collection("companies")
            .whereEqualTo("userId", userId)
            .get()
            .addOnSuccessListener { documents ->
                if (!documents.isEmpty) {
                    // User is a company, save token to company document
                    val companyDoc = documents.documents[0]
                    companyDoc.reference.update("fcmToken", token)
                        .addOnSuccessListener {
                            Log.d(TAG, "FCM Token saved for company: ${companyDoc.id}")
                        }
                        .addOnFailureListener { e ->
                            Log.e(TAG, "Error saving FCM token for company", e)
                        }
                }
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error checking if user is company", e)
            }
    }
}
