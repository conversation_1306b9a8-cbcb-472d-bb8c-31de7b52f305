package com.example.jobrec.ui.theme

import androidx.compose.ui.graphics.Color

// Light Theme Colors
val Red700 = Color(0xFFD32F2F)
val Red900 = Color(0xFFB71C1C)
val Red100 = Color(0xFFFFCDD2)
val Red200 = Color(0xFFEF9A9A)
val Red400 = Color(0xFFEF5350)
val Red500 = Color(0xFFF44336)

// Dark Theme Colors
val Red300 = Color(0xFFE57373)
val Red800 = Color(0xFFC62828)
val Red50 = Color(0xFFFFEBEE)

// Background Colors
val LightBackground = Color(0xFFF5F7FA)
val DarkBackground = Color(0xFF121212)
val LightSurface = Color(0xFFFFFFFF)
val DarkSurface = Color(0xFF1E1E1E)

// Text Colors
val LightTextPrimary = Color(0xFF263238)
val LightTextSecondary = Color(0xFF607D8B)
val DarkTextPrimary = Color(0xFFFFFFFF)
val DarkTextSecondary = Color(0xB3FFFFFF) // 70% white

// Status Colors
val StatusPending = Color(0xFFFFC107)
val StatusReviewed = Color(0xFF2196F3)
val StatusAccepted = Color(0xFF4CAF50)
val StatusRejected = Color(0xFFF44336)
val StatusShortlisted = Color(0xFF9C27B0)
val StatusInterviewing = Color(0xFF009688)
val StatusOffered = Color(0xFFFF9800)