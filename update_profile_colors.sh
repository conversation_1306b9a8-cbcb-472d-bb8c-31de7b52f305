And messages needs to show candidates profile instead of company to make it easier to search for candidates#!/bin/bash

# Update all section titles
sed -i '' 's/android:textColor="@color\/black"/android:textColor="?android:attr\/textColorPrimary"/g' app/src/main/res/layout/activity_profile.xml

# Update all TextInputEditText text colors
sed -i '' 's/android:textColor="@color\/black"/android:textColor="?android:attr\/textColorPrimary"/g' app/src/main/res/layout/activity_profile.xml

# Update all button text colors
sed -i '' 's/style="@style\/Widget.MaterialComponents.Button.TextButton"\n                        android:textColor="@color\/black"/style="@style\/Widget.MaterialComponents.Button.TextButton"\n                        android:textColor="?attr\/colorPrimary"/g' app/src/main/res/layout/activity_profile.xml

echo "Updated all hardcoded colors in Profile activity"
